<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家庭日程助手</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#A5B4FC'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0.5rem;
        }
        
        .calendar-day {
            aspect-ratio: 1;
            position: relative;
            overflow: hidden;
        }
        
        .event-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 3px;
        }
        
        .today {
            background-color: rgba(79, 70, 229, 0.1);
            border: 2px solid #4F46E5;
        }
        
        @media (max-width: 768px) {
            .calendar-day {
                min-height: 60px;
            }
        }
        
        .custom-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        
        .custom-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e2e8f0;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #4F46E5;
        }
        
        input:checked + .slider:before {
            transform: translateX(20px);
        }
        
        input[type="range"] {
            -webkit-appearance: none;
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 5px;
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #4F46E5;
            border-radius: 50%;
            cursor: pointer;
        }
        
        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #4F46E5;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .custom-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        .checkmark {
            height: 20px;
            width: 20px;
            background-color: #fff;
            border: 2px solid #e2e8f0;
            border-radius: 4px;
            position: relative;
        }
        
        .custom-checkbox input:checked ~ .checkmark {
            background-color: #4F46E5;
            border-color: #4F46E5;
        }
        
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }
        
        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }
        
        .custom-checkbox .checkmark:after {
            left: 6px;
            top: 2px;
            width: 6px;
            height: 11px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        .custom-radio {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .custom-radio input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        .radio-mark {
            height: 20px;
            width: 20px;
            background-color: #fff;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            position: relative;
        }
        
        .custom-radio input:checked ~ .radio-mark {
            border-color: #4F46E5;
        }
        
        .radio-mark:after {
            content: "";
            position: absolute;
            display: none;
            top: 4px;
            left: 4px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4F46E5;
        }
        
        .custom-radio input:checked ~ .radio-mark:after {
            display: block;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            min-width: 200px;
            z-index: 50;
        }
        
        .dropdown:hover .dropdown-content {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-10">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <span class="text-2xl font-['Pacifico'] text-primary">logo</span>
                <h1 class="text-xl font-semibold hidden md:block">家庭日程助手</h1>
            </div>
            
            <div class="flex items-center space-x-4">
                <button class="md:hidden bg-white p-2 rounded-full shadow-sm" id="mobile-menu-button">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <i class="ri-menu-line text-gray-700 ri-lg"></i>
                    </div>
                </button>
                
                <div class="dropdown relative">
                    <button class="flex items-center space-x-2 bg-white rounded-full">
                        <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <i class="ri-user-line text-primary ri-lg"></i>
                        </div>
                    </button>
                    <div class="dropdown-content bg-white shadow-lg rounded mt-2 py-2">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">个人设置</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">家庭设置</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">通知中心</a>
                        <div class="border-t border-gray-100 my-1"></div>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="container mx-auto pt-16 pb-20 md:pb-8 px-4 flex flex-col md:flex-row min-h-screen">
        <!-- 左侧边栏 - 家庭成员管理 -->
        <aside class="w-full md:w-64 lg:w-72 md:pr-4 hidden md:block" id="sidebar">
            <div class="bg-white rounded shadow-sm p-4 sticky top-20">
                <h2 class="text-lg font-semibold mb-4">家庭成员</h2>
                
                <ul class="space-y-3 mb-4">
                    <li>
                        <button class="w-full flex items-center space-x-3 p-2 rounded hover:bg-gray-50 transition-colors">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <i class="ri-user-line text-blue-500 ri-lg"></i>
                            </div>
                            <span class="text-gray-800 font-medium">陈明</span>
                            <div class="ml-auto w-3 h-3 rounded-full bg-blue-500"></div>
                        </button>
                    </li>
                    <li>
                        <button class="w-full flex items-center space-x-3 p-2 rounded hover:bg-gray-50 transition-colors">
                            <div class="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center">
                                <i class="ri-user-line text-pink-500 ri-lg"></i>
                            </div>
                            <span class="text-gray-800 font-medium">李婷婷</span>
                            <div class="ml-auto w-3 h-3 rounded-full bg-pink-500"></div>
                        </button>
                    </li>
                    <li>
                        <button class="w-full flex items-center space-x-3 p-2 rounded hover:bg-gray-50 transition-colors">
                            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                                <i class="ri-user-line text-green-500 ri-lg"></i>
                            </div>
                            <span class="text-gray-800 font-medium">小明</span>
                            <div class="ml-auto w-3 h-3 rounded-full bg-green-500"></div>
                        </button>
                    </li>
                    <li>
                        <button class="w-full flex items-center space-x-3 p-2 rounded hover:bg-gray-50 transition-colors">
                            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                                <i class="ri-user-line text-purple-500 ri-lg"></i>
                            </div>
                            <span class="text-gray-800 font-medium">小红</span>
                            <div class="ml-auto w-3 h-3 rounded-full bg-purple-500"></div>
                        </button>
                    </li>
                </ul>
                
                <button class="w-full flex items-center justify-center space-x-2 p-2 border border-dashed border-gray-300 rounded text-gray-600 hover:bg-gray-50 transition-colors !rounded-button whitespace-nowrap">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-add-line"></i>
                    </div>
                    <span>添加家庭成员</span>
                </button>
                
                <div class="mt-6 pt-5 border-t border-gray-100">
                    <h3 class="text-sm font-medium text-gray-500 mb-3">日程显示</h3>
                    
                    <div class="space-y-3">
                        <label class="flex items-center justify-between cursor-pointer">
                            <span class="text-gray-700 flex items-center">
                                <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                                陈明
                            </span>
                            <label class="custom-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </label>
                        
                        <label class="flex items-center justify-between cursor-pointer">
                            <span class="text-gray-700 flex items-center">
                                <span class="w-3 h-3 rounded-full bg-pink-500 mr-2"></span>
                                李婷婷
                            </span>
                            <label class="custom-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </label>
                        
                        <label class="flex items-center justify-between cursor-pointer">
                            <span class="text-gray-700 flex items-center">
                                <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                                小明
                            </span>
                            <label class="custom-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </label>
                        
                        <label class="flex items-center justify-between cursor-pointer">
                            <span class="text-gray-700 flex items-center">
                                <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                                小红
                            </span>
                            <label class="custom-switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </label>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 中央区域 - 日历视图 -->
        <section class="w-full md:flex-1 md:px-4 mt-4 md:mt-0">
            <div class="bg-white rounded shadow-sm p-4">
                <!-- 日历控制区 -->
                <div class="flex flex-wrap justify-between items-center mb-6">
                    <div class="flex items-center space-x-2 mb-2 sm:mb-0">
                        <h2 class="text-2xl font-semibold">2025 年 7 月</h2>
                        <div class="flex space-x-1">
                            <button class="p-1 rounded hover:bg-gray-100 !rounded-button whitespace-nowrap">
                                <div class="w-8 h-8 flex items-center justify-center">
                                    <i class="ri-arrow-left-s-line ri-lg"></i>
                                </div>
                            </button>
                            <button class="p-1 rounded hover:bg-gray-100 !rounded-button whitespace-nowrap">
                                <div class="w-8 h-8 flex items-center justify-center">
                                    <i class="ri-arrow-right-s-line ri-lg"></i>
                                </div>
                            </button>
                        </div>
                        <button class="text-sm text-primary font-medium hover:underline !rounded-button whitespace-nowrap">今天</button>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <div class="bg-gray-100 rounded-full p-1 flex">
                            <button class="px-3 py-1 rounded-full bg-primary text-white !rounded-button whitespace-nowrap">月</button>
                            <button class="px-3 py-1 rounded-full text-gray-700 hover:bg-gray-200 !rounded-button whitespace-nowrap">周</button>
                            <button class="px-3 py-1 rounded-full text-gray-700 hover:bg-gray-200 !rounded-button whitespace-nowrap">日</button>
                        </div>
                        
                        <button class="bg-primary text-white px-4 py-2 rounded flex items-center space-x-1 !rounded-button whitespace-nowrap">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-add-line"></i>
                            </div>
                            <span>添加事件</span>
                        </button>
                    </div>
                </div>
                
                <!-- 星期标题 -->
                <div class="calendar-grid mb-2">
                    <div class="text-center text-gray-500 font-medium">周日</div>
                    <div class="text-center text-gray-500 font-medium">周一</div>
                    <div class="text-center text-gray-500 font-medium">周二</div>
                    <div class="text-center text-gray-500 font-medium">周三</div>
                    <div class="text-center text-gray-500 font-medium">周四</div>
                    <div class="text-center text-gray-500 font-medium">周五</div>
                    <div class="text-center text-gray-500 font-medium">周六</div>
                </div>
                
                <!-- 日历网格 -->
                <div class="calendar-grid">
                    <!-- 上个月日期 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded bg-gray-50">
                        <div class="text-gray-400 text-sm">30</div>
                    </div>
                    
                    <!-- 7月日期 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">1</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                早会 9:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300 today">
                        <div class="text-primary font-bold text-sm">2</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-pink-100 text-pink-800 rounded mb-1 truncate">
                                购物 15:00
                            </div>
                            <div class="text-xs px-1 py-0.5 bg-green-100 text-green-800 rounded mb-1 truncate">
                                钢琴课 17:30
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">3</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">4</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-purple-100 text-purple-800 rounded mb-1 truncate">
                                舞蹈课 16:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">5</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">6</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                家庭聚餐 18:00
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第二周 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">7</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">8</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                项目会议 10:30
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">9</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">10</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-green-100 text-green-800 rounded mb-1 truncate">
                                数学补习 16:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">11</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">12</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-pink-100 text-pink-800 rounded mb-1 truncate">
                                美容院 14:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">13</div>
                    </div>
                    
                    <!-- 第三周 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">14</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">15</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                牙医预约 9:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">16</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">17</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-purple-100 text-purple-800 rounded mb-1 truncate">
                                绘画课 15:30
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">18</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">19</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-green-100 text-green-800 rounded mb-1 truncate">
                                足球训练 16:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">20</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                家庭旅行
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第四周 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">21</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                家庭旅行
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">22</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">23</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-pink-100 text-pink-800 rounded mb-1 truncate">
                                瑜伽课 18:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">24</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">25</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                客户会议 14:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">26</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">27</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-green-100 text-green-800 rounded mb-1 truncate">
                                英语课 10:00
                            </div>
                        </div>
                    </div>
                    
                    <!-- 第五周 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">28</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">29</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-purple-100 text-purple-800 rounded mb-1 truncate">
                                舞蹈表演 19:00
                            </div>
                        </div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">30</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded hover:border-gray-300">
                        <div class="text-gray-700 text-sm">31</div>
                        <div class="mt-1">
                            <div class="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded mb-1 truncate">
                                月度总结 16:00
                            </div>
                        </div>
                    </div>
                    
                    <!-- 下个月日期 -->
                    <div class="calendar-day p-1 border border-gray-100 rounded bg-gray-50">
                        <div class="text-gray-400 text-sm">1</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded bg-gray-50">
                        <div class="text-gray-400 text-sm">2</div>
                    </div>
                    
                    <div class="calendar-day p-1 border border-gray-100 rounded bg-gray-50">
                        <div class="text-gray-400 text-sm">3</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 右侧边栏 - 事件管理 -->
        <aside class="w-full md:w-64 lg:w-80 md:pl-4 mt-4 md:mt-0 hidden md:block" id="event-sidebar">
            <div class="bg-white rounded shadow-sm p-4 sticky top-20">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">今日事件</h2>
                    <span class="text-sm text-gray-500">2025 年 7 月 2 日</span>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">购物</h3>
                                <p class="text-sm text-gray-500 mt-1">15:00 - 16:30</p>
                            </div>
                            <div class="flex space-x-1">
                                <button class="p-1 text-gray-400 hover:text-gray-700 !rounded-button whitespace-nowrap">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-edit-line"></i>
                                    </div>
                                </button>
                                <button class="p-1 text-gray-400 hover:text-red-500 !rounded-button whitespace-nowrap">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center mt-2">
                            <span class="w-2 h-2 rounded-full bg-pink-500 mr-2"></span>
                            <span class="text-xs text-gray-500">李婷婷</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">超市购物，需要买水果、蔬菜和日用品</p>
                    </div>
                    
                    <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">钢琴课</h3>
                                <p class="text-sm text-gray-500 mt-1">17:30 - 18:30</p>
                            </div>
                            <div class="flex space-x-1">
                                <button class="p-1 text-gray-400 hover:text-gray-700 !rounded-button whitespace-nowrap">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-edit-line"></i>
                                    </div>
                                </button>
                                <button class="p-1 text-gray-400 hover:text-red-500 !rounded-button whitespace-nowrap">
                                    <div class="w-6 h-6 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center mt-2">
                            <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-xs text-gray-500">小明</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-2">钢琴课，地点：音乐学校，记得带乐谱</p>
                    </div>
                </div>
                
                <div class="border-t border-gray-100 pt-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-3">即将到来</h3>
                    
                    <div class="space-y-3">
                        <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="font-medium">舞蹈课</h3>
                                    <p class="text-sm text-gray-500 mt-1">7 月 4 日 16:00</p>
                                </div>
                            </div>
                            <div class="flex items-center mt-2">
                                <span class="w-2 h-2 rounded-full bg-purple-500 mr-2"></span>
                                <span class="text-xs text-gray-500">小红</span>
                            </div>
                        </div>
                        
                        <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="font-medium">家庭聚餐</h3>
                                    <p class="text-sm text-gray-500 mt-1">7 月 6 日 18:00</p>
                                </div>
                            </div>
                            <div class="flex items-center mt-2 space-x-2">
                                <div class="flex">
                                    <span class="w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
                                    <span class="text-xs text-gray-500">陈明</span>
                                </div>
                                <div class="flex">
                                    <span class="w-2 h-2 rounded-full bg-pink-500 mr-1"></span>
                                    <span class="text-xs text-gray-500">李婷婷</span>
                                </div>
                                <div class="flex">
                                    <span class="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                                    <span class="text-xs text-gray-500">小明</span>
                                </div>
                                <div class="flex">
                                    <span class="w-2 h-2 rounded-full bg-purple-500 mr-1"></span>
                                    <span class="text-xs text-gray-500">小红</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </main>

    <!-- 移动端底部导航 -->
    <nav class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
        <div class="flex justify-around">
            <button class="flex flex-col items-center py-2 px-4 text-primary" id="calendar-tab">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-calendar-line"></i>
                </div>
                <span class="text-xs mt-1">日历</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-gray-500" id="events-tab">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-list-check"></i>
                </div>
                <span class="text-xs mt-1">事件</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-gray-500" id="members-tab">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-team-line"></i>
                </div>
                <span class="text-xs mt-1">成员</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-gray-500" id="add-event-tab">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-add-circle-line"></i>
                </div>
                <span class="text-xs mt-1">添加</span>
            </button>
        </div>
    </nav>

    <!-- 添加/编辑事件模态框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center" id="event-modal">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden">
            <div class="p-4 border-b border-gray-100 flex justify-between items-center">
                <h3 class="text-lg font-semibold">添加事件</h3>
                <button class="text-gray-400 hover:text-gray-600" id="close-modal">
                    <div class="w-6 h-6 flex items-center justify-center">
                        <i class="ri-close-line"></i>
                    </div>
                </button>
            </div>
            
            <div class="p-4">
                <form>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2" for="event-title">
                            事件标题
                        </label>
                        <input class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary" id="event-title" type="text" placeholder="输入事件标题">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-gray-700 text-sm font-medium mb-2" for="event-date">
                                日期
                            </label>
                            <input class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary" id="event-date" type="date" value="2025-07-02">
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 text-sm font-medium mb-2" for="event-time">
                                时间
                            </label>
                            <input class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary" id="event-time" type="time" value="09:00">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            参与成员
                        </label>
                        <div class="space-y-2">
                            <label class="custom-checkbox flex items-center">
                                <input type="checkbox" checked>
                                <span class="checkmark mr-2"></span>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                                    <span>陈明</span>
                                </div>
                            </label>
                            
                            <label class="custom-checkbox flex items-center">
                                <input type="checkbox">
                                <span class="checkmark mr-2"></span>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-pink-500 mr-2"></span>
                                    <span>李婷婷</span>
                                </div>
                            </label>
                            
                            <label class="custom-checkbox flex items-center">
                                <input type="checkbox">
                                <span class="checkmark mr-2"></span>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                                    <span>小明</span>
                                </div>
                            </label>
                            
                            <label class="custom-checkbox flex items-center">
                                <input type="checkbox">
                                <span class="checkmark mr-2"></span>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                                    <span>小红</span>
                                </div>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2" for="event-description">
                            事件描述
                        </label>
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary" id="event-description" rows="3" placeholder="输入事件描述（可选）"></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            重复
                        </label>
                        <div class="space-y-2">
                            <label class="custom-radio flex items-center">
                                <input type="radio" name="repeat" checked>
                                <span class="radio-mark mr-2"></span>
                                <span>不重复</span>
                            </label>
                            
                            <label class="custom-radio flex items-center">
                                <input type="radio" name="repeat">
                                <span class="radio-mark mr-2"></span>
                                <span>每天</span>
                            </label>
                            
                            <label class="custom-radio flex items-center">
                                <input type="radio" name="repeat">
                                <span class="radio-mark mr-2"></span>
                                <span>每周</span>
                            </label>
                            
                            <label class="custom-radio flex items-center">
                                <input type="radio" name="repeat">
                                <span class="radio-mark mr-2"></span>
                                <span>每月</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-2 pt-4 border-t border-gray-100">
                        <button type="button" class="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap" id="cancel-event">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 !rounded-button whitespace-nowrap" id="save-event">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 移动端成员管理模块 -->
    <div class="md:hidden fixed inset-0 bg-white z-40 hidden overflow-auto pt-16 pb-14" id="mobile-members">
        <div class="p-4">
            <h2 class="text-lg font-semibold mb-4">家庭成员</h2>
            
            <ul class="space-y-3 mb-4">
                <li>
                    <button class="w-full flex items-center space-x-3 p-3 rounded border border-gray-100 hover:border-gray-300 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <i class="ri-user-line text-blue-500 ri-lg"></i>
                        </div>
                        <div class="flex-1 text-left">
                            <span class="text-gray-800 font-medium">陈明</span>
                            <p class="text-xs text-gray-500 mt-1">家庭主要负责人</p>
                        </div>
                        <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                    </button>
                </li>
                <li>
                    <button class="w-full flex items-center space-x-3 p-3 rounded border border-gray-100 hover:border-gray-300 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center">
                            <i class="ri-user-line text-pink-500 ri-lg"></i>
                        </div>
                        <div class="flex-1 text-left">
                            <span class="text-gray-800 font-medium">李婷婷</span>
                            <p class="text-xs text-gray-500 mt-1">家庭成员</p>
                        </div>
                        <div class="w-3 h-3 rounded-full bg-pink-500"></div>
                    </button>
                </li>
                <li>
                    <button class="w-full flex items-center space-x-3 p-3 rounded border border-gray-100 hover:border-gray-300 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                            <i class="ri-user-line text-green-500 ri-lg"></i>
                        </div>
                        <div class="flex-1 text-left">
                            <span class="text-gray-800 font-medium">小明</span>
                            <p class="text-xs text-gray-500 mt-1">孩子</p>
                        </div>
                        <div class="w-3 h-3 rounded-full bg-green-500"></div>
                    </button>
                </li>
                <li>
                    <button class="w-full flex items-center space-x-3 p-3 rounded border border-gray-100 hover:border-gray-300 transition-colors">
                        <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                            <i class="ri-user-line text-purple-500 ri-lg"></i>
                        </div>
                        <div class="flex-1 text-left">
                            <span class="text-gray-800 font-medium">小红</span>
                            <p class="text-xs text-gray-500 mt-1">孩子</p>
                        </div>
                        <div class="w-3 h-3 rounded-full bg-purple-500"></div>
                    </button>
                </li>
            </ul>
            
            <button class="w-full flex items-center justify-center space-x-2 p-3 border border-dashed border-gray-300 rounded text-gray-600 hover:bg-gray-50 transition-colors !rounded-button whitespace-nowrap">
                <div class="w-6 h-6 flex items-center justify-center">
                    <i class="ri-add-line"></i>
                </div>
                <span>添加家庭成员</span>
            </button>
            
            <div class="mt-6 pt-5 border-t border-gray-100">
                <h3 class="text-sm font-medium text-gray-500 mb-3">日程显示</h3>
                
                <div class="space-y-4">
                    <label class="flex items-center justify-between cursor-pointer">
                        <span class="text-gray-700 flex items-center">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            陈明
                        </span>
                        <label class="custom-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                    
                    <label class="flex items-center justify-between cursor-pointer">
                        <span class="text-gray-700 flex items-center">
                            <span class="w-3 h-3 rounded-full bg-pink-500 mr-2"></span>
                            李婷婷
                        </span>
                        <label class="custom-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                    
                    <label class="flex items-center justify-between cursor-pointer">
                        <span class="text-gray-700 flex items-center">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            小明
                        </span>
                        <label class="custom-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                    
                    <label class="flex items-center justify-between cursor-pointer">
                        <span class="text-gray-700 flex items-center">
                            <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                            小红
                        </span>
                        <label class="custom-switch">
                            <input type="checkbox" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端事件列表模块 -->
    <div class="md:hidden fixed inset-0 bg-white z-40 hidden overflow-auto pt-16 pb-14" id="mobile-events">
        <div class="p-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold">今日事件</h2>
                <span class="text-sm text-gray-500">2025 年 7 月 2 日</span>
            </div>
            
            <div class="space-y-3 mb-6">
                <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                    <div class="flex items-start justify-between">
                        <div>
                            <h3 class="font-medium">购物</h3>
                            <p class="text-sm text-gray-500 mt-1">15:00 - 16:30</p>
                        </div>
                        <div class="flex space-x-1">
                            <button class="p-1 text-gray-400 hover:text-gray-700 !rounded-button whitespace-nowrap">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </button>
                            <button class="p-1 text-gray-400 hover:text-red-500 !rounded-button whitespace-nowrap">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <i class="ri-delete-bin-line"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center mt-2">
                        <span class="w-2 h-2 rounded-full bg-pink-500 mr-2"></span>
                        <span class="text-xs text-gray-500">李婷婷</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">超市购物，需要买水果、蔬菜和日用品</p>
                </div>
                
                <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                    <div class="flex items-start justify-between">
                        <div>
                            <h3 class="font-medium">钢琴课</h3>
                            <p class="text-sm text-gray-500 mt-1">17:30 - 18:30</p>
                        </div>
                        <div class="flex space-x-1">
                            <button class="p-1 text-gray-400 hover:text-gray-700 !rounded-button whitespace-nowrap">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </button>
                            <button class="p-1 text-gray-400 hover:text-red-500 !rounded-button whitespace-nowrap">
                                <div class="w-6 h-6 flex items-center justify-center">
                                    <i class="ri-delete-bin-line"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center mt-2">
                        <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                        <span class="text-xs text-gray-500">小明</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">钢琴课，地点：音乐学校，记得带乐谱</p>
                </div>
            </div>
            
            <div class="border-t border-gray-100 pt-4">
                <h3 class="text-sm font-medium text-gray-500 mb-3">即将到来</h3>
                
                <div class="space-y-3">
                    <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">舞蹈课</h3>
                                <p class="text-sm text-gray-500 mt-1">7 月 4 日 16:00</p>
                            </div>
                        </div>
                        <div class="flex items-center mt-2">
                            <span class="w-2 h-2 rounded-full bg-purple-500 mr-2"></span>
                            <span class="text-xs text-gray-500">小红</span>
                        </div>
                    </div>
                    
                    <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">家庭聚餐</h3>
                                <p class="text-sm text-gray-500 mt-1">7 月 6 日 18:00</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap items-center mt-2 gap-2">
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-blue-500 mr-1"></span>
                                <span class="text-xs text-gray-500">陈明</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-pink-500 mr-1"></span>
                                <span class="text-xs text-gray-500">李婷婷</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                                <span class="text-xs text-gray-500">小明</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-purple-500 mr-1"></span>
                                <span class="text-xs text-gray-500">小红</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">项目会议</h3>
                                <p class="text-sm text-gray-500 mt-1">7 月 8 日 10:30</p>
                            </div>
                        </div>
                        <div class="flex items-center mt-2">
                            <span class="w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-xs text-gray-500">陈明</span>
                        </div>
                    </div>
                    
                    <div class="p-3 border border-gray-100 rounded hover:border-gray-300 transition-colors">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-medium">数学补习</h3>
                                <p class="text-sm text-gray-500 mt-1">7 月 10 日 16:00</p>
                            </div>
                        </div>
                        <div class="flex items-center mt-2">
                            <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-xs text-gray-500">小明</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script id="mobile-navigation">
        document.addEventListener('DOMContentLoaded', function() {
            const calendarTab = document.getElementById('calendar-tab');
            const eventsTab = document.getElementById('events-tab');
            const membersTab = document.getElementById('members-tab');
            const addEventTab = document.getElementById('add-event-tab');
            
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMembers = document.getElementById('mobile-members');
            const mobileEvents = document.getElementById('mobile-events');
            
            // 默认显示日历视图
            calendarTab.classList.add('text-primary');
            eventsTab.classList.remove('text-primary');
            membersTab.classList.remove('text-primary');
            addEventTab.classList.remove('text-primary');
            
            mobileMembers.classList.add('hidden');
            mobileEvents.classList.add('hidden');
            
            // 切换到日历视图
            calendarTab.addEventListener('click', function() {
                calendarTab.classList.add('text-primary');
                eventsTab.classList.remove('text-primary');
                membersTab.classList.remove('text-primary');
                addEventTab.classList.remove('text-primary');
                
                mobileMembers.classList.add('hidden');
                mobileEvents.classList.add('hidden');
            });
            
            // 切换到事件视图
            eventsTab.addEventListener('click', function() {
                calendarTab.classList.remove('text-primary');
                eventsTab.classList.add('text-primary');
                membersTab.classList.remove('text-primary');
                addEventTab.classList.remove('text-primary');
                
                mobileMembers.classList.add('hidden');
                mobileEvents.classList.remove('hidden');
            });
            
            // 切换到成员视图
            membersTab.addEventListener('click', function() {
                calendarTab.classList.remove('text-primary');
                eventsTab.classList.remove('text-primary');
                membersTab.classList.add('text-primary');
                addEventTab.classList.remove('text-primary');
                
                mobileMembers.classList.remove('hidden');
                mobileEvents.classList.add('hidden');
            });
            
            // 打开添加事件模态框
            addEventTab.addEventListener('click', function() {
                document.getElementById('event-modal').classList.remove('hidden');
            });
            
            // 移动端菜单按钮
            mobileMenuButton.addEventListener('click', function() {
                if (mobileMembers.classList.contains('hidden')) {
                    mobileMembers.classList.remove('hidden');
                    mobileEvents.classList.add('hidden');
                    
                    calendarTab.classList.remove('text-primary');
                    eventsTab.classList.remove('text-primary');
                    membersTab.classList.add('text-primary');
                    addEventTab.classList.remove('text-primary');
                } else {
                    mobileMembers.classList.add('hidden');
                    
                    calendarTab.classList.add('text-primary');
                    eventsTab.classList.remove('text-primary');
                    membersTab.classList.remove('text-primary');
                    addEventTab.classList.remove('text-primary');
                }
            });
        });
    </script>

    <script id="event-modal-handler">
        document.addEventListener('DOMContentLoaded', function() {
            const eventModal = document.getElementById('event-modal');
            const closeModal = document.getElementById('close-modal');
            const cancelEvent = document.getElementById('cancel-event');
            const saveEvent = document.getElementById('save-event');
            
            // 页面上所有可以打开模态框的按钮
            const addEventButtons = document.querySelectorAll('button.bg-primary:not(#save-event)');
            
            // 打开模态框
            addEventButtons.forEach(button => {
                button.addEventListener('click', function() {
                    eventModal.classList.remove('hidden');
                });
            });
            
            // 关闭模态框
            closeModal.addEventListener('click', function() {
                eventModal.classList.add('hidden');
            });
            
            cancelEvent.addEventListener('click', function() {
                eventModal.classList.add('hidden');
            });
            
            saveEvent.addEventListener('click', function() {
                // 这里可以添加保存事件的逻辑
                eventModal.classList.add('hidden');
                
                // 模拟添加成功提示
                alert('事件添加成功！');
            });
            
            // 点击模态框外部关闭
            eventModal.addEventListener('click', function(e) {
                if (e.target === eventModal) {
                    eventModal.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>