import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, User } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { FamilyMember } from '@/types'

interface SidebarProps {
  familyMembers: FamilyMember[]
  onAddMember?: () => void
  onToggleMemberVisibility?: (memberId: string, isVisible: boolean) => void
  className?: string
}

const Sidebar: React.FC<SidebarProps> = ({
  familyMembers,
  onAddMember,
  onToggleMemberVisibility,
  className
}) => {
  return (
    <aside className={cn(
      "w-full md:w-64 lg:w-72 md:pr-4 hidden md:block",
      className
    )}>
      <Card className="sticky top-20">
        <CardHeader>
          <CardTitle className="text-lg">家庭成员</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 成员列表 */}
          <div className="space-y-3">
            {familyMembers.map((member) => (
              <Button
                key={member.id}
                variant="ghost"
                className="w-full flex items-center justify-start space-x-3 p-3 h-auto"
              >
                <div 
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: `${member.color}20` }}
                >
                  <User 
                    className="h-5 w-5" 
                    style={{ color: member.color }}
                  />
                </div>
                <span className="text-gray-800 font-medium flex-1 text-left">
                  {member.name}
                </span>
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: member.color }}
                />
              </Button>
            ))}
          </div>
          
          {/* 添加成员按钮 */}
          <Button
            variant="outline"
            className="w-full flex items-center justify-center space-x-2 border-dashed"
            onClick={onAddMember}
          >
            <Plus className="h-4 w-4" />
            <span>添加家庭成员</span>
          </Button>
          
          {/* 显示控制 */}
          <div className="pt-4 border-t border-gray-100">
            <h3 className="text-sm font-medium text-gray-500 mb-3">
              日程显示
            </h3>
            
            <div className="space-y-3">
              {familyMembers.map((member) => (
                <div 
                  key={member.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: member.color }}
                    />
                    <span className="text-sm text-gray-700">
                      {member.name}
                    </span>
                  </div>
                  <Switch
                    checked={member.isVisible}
                    onCheckedChange={(checked) => 
                      onToggleMemberVisibility?.(member.id, checked)
                    }
                  />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </aside>
  )
}

export default Sidebar
