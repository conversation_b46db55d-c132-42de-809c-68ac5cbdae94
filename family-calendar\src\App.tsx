import React, { useState } from 'react'
import { Header, Sidebar, MobileNavigation } from '@/components/layout'
import type { FamilyMember } from '@/types'

// 模拟数据
const mockFamilyMembers: FamilyMember[] = [
  {
    id: '1',
    name: '陈明',
    role: 'parent',
    color: '#4F46E5',
    isVisible: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    name: '李婷婷',
    role: 'parent',
    color: '#EC4899',
    isVisible: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    name: '小明',
    role: 'child',
    color: '#10B981',
    isVisible: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '4',
    name: '小红',
    role: 'child',
    color: '#8B5CF6',
    isVisible: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
]

function App() {
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>(mockFamilyMembers)
  const [activeTab, setActiveTab] = useState<'calendar' | 'events' | 'members' | 'add'>('calendar')
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleToggleMemberVisibility = (memberId: string, isVisible: boolean) => {
    setFamilyMembers(prev =>
      prev.map(member =>
        member.id === memberId
          ? { ...member, isVisible }
          : member
      )
    )
  }

  const handleAddMember = () => {
    console.log('添加家庭成员')
  }

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header onMobileMenuToggle={handleMobileMenuToggle} />

      <main className="container mx-auto pt-16 pb-20 md:pb-8 px-4 flex flex-col md:flex-row min-h-screen">
        <Sidebar
          familyMembers={familyMembers}
          onAddMember={handleAddMember}
          onToggleMemberVisibility={handleToggleMemberVisibility}
        />

        <section className="w-full md:flex-1 md:px-4 mt-4 md:mt-0">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-2xl font-semibold mb-4">
              {activeTab === 'calendar' && '日历视图'}
              {activeTab === 'events' && '事件管理'}
              {activeTab === 'members' && '家庭成员'}
              {activeTab === 'add' && '添加事件'}
            </h2>
            <p className="text-gray-600">
              这里将显示{activeTab === 'calendar' ? '日历' : activeTab === 'events' ? '事件列表' : activeTab === 'members' ? '家庭成员管理' : '添加事件表单'}内容
            </p>
          </div>
        </section>
      </main>

      <MobileNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </div>
  )
}

export default App
