import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar, CheckSquare, Users, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MobileNavigationProps {
  activeTab: 'calendar' | 'events' | 'members' | 'add'
  onTabChange: (tab: 'calendar' | 'events' | 'members' | 'add') => void
  className?: string
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({
  activeTab,
  onTabChange,
  className
}) => {
  const tabs = [
    {
      id: 'calendar' as const,
      label: '日历',
      icon: Calendar,
    },
    {
      id: 'events' as const,
      label: '事件',
      icon: CheckSquare,
    },
    {
      id: 'members' as const,
      label: '成员',
      icon: Users,
    },
    {
      id: 'add' as const,
      label: '添加',
      icon: Plus,
    },
  ]

  return (
    <nav className={cn(
      "md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10",
      className
    )}>
      <div className="flex justify-around">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          
          return (
            <Button
              key={tab.id}
              variant="ghost"
              className={cn(
                "flex flex-col items-center py-2 px-4 h-auto space-y-1",
                isActive ? "text-primary" : "text-gray-500"
              )}
              onClick={() => onTabChange(tab.id)}
            >
              <Icon className="h-5 w-5" />
              <span className="text-xs">{tab.label}</span>
            </Button>
          )
        })}
      </div>
    </nav>
  )
}

export default MobileNavigation
